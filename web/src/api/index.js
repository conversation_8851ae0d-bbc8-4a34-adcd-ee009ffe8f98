import { request } from '@/utils'
import axios from 'axios'

// 创建一个检测 URL 是否已包含 /api/v1 的函数
const getBaseUrl = () => {
  // 使用当前窗口的location.origin作为基础URL，确保使用当前访问的服务器地址
  // 如果有环境变量设置，优先使用环境变量
  const baseApi = import.meta.env.VITE_BASE_API || window.location.origin;
  console.log('使用API基础URL:', baseApi);

  // 如果 baseApi 已经包含 /api/v1，则直接返回
  if (baseApi.includes('/api/v1')) {
    return baseApi;
  }
  // 否则，添加 /api/v1 前缀
  return `${baseApi}/api/v1`;
};

const loginAxios = axios.create({
  baseURL: getBaseUrl(),
  timeout: 12000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

loginAxios.interceptors.response.use(
  response => {
    console.log('响应成功:', response.config.url, response.status);
    return response;
  },
  error => {
    console.error('响应错误:', error.config?.url, error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    return Promise.reject(error);
  }
)

export default {
  login: async (data) => {
    console.log('发送登录请求:', data);
    try {
      // 尝试不同的登录路径
      let response;
      try {
        console.log('尝试路径: /auth/login');
        response = await loginAxios({
          method: 'post',
          url: '/auth/login',
          data: {
            username: data.username,
            password: data.password
          }
        });
      } catch (error) {
        if (error.response && error.response.status === 404) {
          console.log('尝试备用路径: /base/access_token');
          response = await loginAxios({
            method: 'post',
            url: '/base/access_token',
            data: {
              username: data.username,
              password: data.password
            }
          });
        } else {
          throw error;
        }
      }

      console.log('登录响应:', response);

      if (response.data && response.data.access_token) {
        return {
          code: 200,
          data: response.data
        };
      }
      throw new Error('Invalid response format');
    } catch (error) {
      console.error('登录请求失败:', error);
      throw error;
    }
  },
  getUserInfo: () => request.get('/base/userinfo'),
  getUserMenu: () => request.get('/base/usermenu'),
  getUserApi: () => request.get('/base/userapi'),
  // profile
  updatePassword: (data = {}) => request.post('/base/update_password', data),
  // users
  getUserList: (params = {}) => request.get('/user/list', { params }),
  getUserById: (params = {}) => request.get('/user/get', { params }),
  createUser: (data = {}) => request.post('/user/create', data),
  updateUser: (data = {}) => request.post('/user/update', data),
  deleteUser: (params = {}) => request.delete(`/user/delete`, { params }),
  resetPassword: (data = {}) => request.post(`/user/reset_password`, data),
  // role
  getRoleList: (params = {}) => request.get('/role/list', { params }),
  createRole: (data = {}) => request.post('/role/create', data),
  updateRole: (data = {}) => request.post('/role/update', data),
  deleteRole: (params = {}) => request.delete('/role/delete', { params }),
  updateRoleAuthorized: (data = {}) => request.post('/role/authorized', data),
  getRoleAuthorized: (params = {}) => request.get('/role/authorized', { params }),
  // menus
  getMenus: (params = {}) => request.get('/menu/list', { params }),
  createMenu: (data = {}) => request.post('/menu/create', data),
  updateMenu: (data = {}) => request.post('/menu/update', data),
  deleteMenu: (params = {}) => request.delete('/menu/delete', { params }),
  // apis
  getApis: (params = {}) => request.get('/api/list', { params }),
  createApi: (data = {}) => request.post('/api/create', data),
  updateApi: (data = {}) => request.post('/api/update', data),
  deleteApi: (params = {}) => request.delete('/api/delete', { params }),
  refreshApi: (data = {}) => request.post('/api/refresh', data),
  // depts
  getDepts: (params = {}) => request.get('/dept/list', { params }),
  createDept: (data = {}) => request.post('/dept/create', data),
  updateDept: (data = {}) => request.post('/dept/update', data),
  deleteDept: (params = {}) => request.delete('/dept/delete', { params }),
  // auditlog
  getAuditLogList: (params = {}) => request.get('/auditlog/list', { params }),
  // projects
  getProjts: (params = {}) => {
    console.log('发送获取项目列表请求:', params)
    return request.get('/project/list', { params })
      .then(response => {
        console.log('获取项目列表成功:', response)
        return response
      })
      .catch(error => {
        console.error('获取项目列表失败:', error)
        throw error
      })
  },
  createProj: (data = {}) => request.post('/project/create', data),
  updateProj: (data = {}) => request.post('/project/update', data),
  deleteProj: (params = {}) => request.delete('/project/delete', { params }),
  // requirements
  // getReqs: (params = {}) => request.get('/requirement/list', { params }),
  getRequirementsList: (params = {}) => request.get('/requirement/list', { params }),
  createRequirement: (data = {}) => request.post('/requirement/create', data),
  updateRequirement: (data = {}) => request.put('/requirement/update', data), // 使用PUT方法与后端API一致
  deleteRequirement: (params = {}) => request.delete('/requirement/delete', { params }),
  chatReq: (data = {}) => request.post('/requirement/chat', data),
  uploadReq: (data = {}) => request.post('/requirement/chat/upload', data),
  // generator
  getGens: (params = {}) => request.get('/dept/list', { params }),
  createGen: (data = {}) => request.post('/dept/create', data),
  updateGen: (data = {}) => request.post('/dept/update', data),
  deleteGen: (params = {}) => request.delete('/dept/delete', { params }),
  // testcases
  getTestCases: (params = {}) => request.get('/testcase/list', { params }),
  createTestCase: (data = {}) => request.post('/testcase/create', data),
  updateTestCase: (data = {}) => request.put('/testcase/update', data), // 使用PUT方法与后端一致
  deleteTestCase: (params = {}) => request.delete('/testcase/delete', { params }),
  updateTestCaseAdoption: (params = {}) => request.put('/testcase/update-adoption', null, { params }), // 更新测试用例采纳状态
  // knowledge
  getKnowledgeItems: (params = {}) => request.get('/knowledge/', { params }),
  getKnowledgeItem: (id) => request.get(`/knowledge/${id}`),
  createKnowledgeItem: (data = {}) => request.post('/knowledge/', data),
  updateKnowledgeItem: (id, data = {}) => request.put(`/knowledge/${id}`, data),
  deleteKnowledgeItem: (id) => request.delete(`/knowledge/${id}`),
  // knowledge relation
  getRequirementKnowledge: (requirementId) => request.get(`/knowledge/relation/requirement/${requirementId}`),

  // dashboard
  getDashboardStats: () => request.get('/dashboard/stats'),
  getProjectDistribution: () => request.get('/dashboard/project-distribution'),
  getRequirementDistribution: (params = {}) => request.get('/dashboard/requirement-distribution', { params }),
  getTestCaseTrend: (params = {}) => request.get('/dashboard/testcase-trend', { params }),
  getKnowledgeDistribution: () => request.get('/dashboard/knowledge-distribution'),
  getActivityTrend: (params = {}) => request.get('/dashboard/activity-trend', { params }),
  getUserContribution: () => request.get('/dashboard/user-contribution'),
   // 新增文件上传方法
  uploadFile: (user_id, file) => {
    const formData = new FormData()
    formData.append('file', file) // 使用'file'作为字段名（更符合单文件语义）
    return request.post(`/agent/upload?user_id=${user_id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  // 获取URL内容
  fetchUrlContent(data) {
    console.log('发送URL获取请求:', data);

    // 打印请求数据，确保格式正确
    console.log('请求数据类型检查:', {
      url: typeof data.url,
      user_id: typeof data.user_id,
      project_id: typeof data.project_id
    });

    // 确保数据类型正确
    const validatedData = {
      url: String(data.url),
      user_id: Number(data.user_id),
      project_id: Number(data.project_id)
    };

    // 尝试不同的URL路径
    const tryFetchUrl = async (path) => {
      try {
        console.log(`尝试路径: ${path}`);
        const response = await loginAxios.post(path, validatedData);
        console.log(`路径 ${path} 请求成功:`, response.status);
        return response;
      } catch (error) {
        console.error(`路径 ${path} 请求失败:`, error.message);
        if (error.response) {
          console.error(`状态码: ${error.response.status}`);
        }
        throw error;
      }
    };

    // 尝试多个路径
    return tryFetchUrl('/requirement/url/fetch')
      .catch(error => {
        // 如果是401错误（需要登录），直接抛出错误
        if (error.response && error.response.status === 401) {
          console.log('TAPD链接需要登录，直接返回401错误');
          throw error;
        }
        // 如果是404错误，尝试备用路径
        else if (error.response && error.response.status === 404) {
          console.log('尝试备用路径...');
          return tryFetchUrl('/requirements/url/fetch');
        }
        throw error;
      })
      .then(response => {
        // 处理响应数据，保持与原来的request对象返回格式一致
        console.log('响应数据:', response.data);
        if (response.data && response.data.code === 200) {
          return response.data;
        } else if (response.data && (response.data.title || response.data.content)) {
          // 兼容直接返回数据的情况
          return {
            code: 200,
            data: response.data
          };
        } else {
          console.error('响应格式不正确:', response.data);
          return Promise.reject({ message: '响应格式不正确', data: response.data });
        }
      });
  },

  // 获取TAPD需求列表
  async getTapdRequirements() {
    try {
      // 直接使用axios而不是request，绕过拦截器
      const axios = (await import('axios')).default;
      // 使用 getBaseUrl 函数获取正确的基础 URL
      const baseUrl = getBaseUrl();
      const response = await axios.get(`${baseUrl}/reqAgent/tapd/list`);
      return response.data;
    } catch (error) {
      console.error('获取TAPD需求列表失败:', error);
      throw error;
    }
  },

  // 删除TAPD需求
  async deleteTapdRequirement(reqId) {
    try {
      // 直接使用axios而不是request，绕过拦截器
      const axios = (await import('axios')).default;
      // 使用 getBaseUrl 函数获取正确的基础 URL
      const baseUrl = getBaseUrl();
      const response = await axios.delete(`${baseUrl}/reqAgent/tapd/delete/${reqId}`);
      return response.data;
    } catch (error) {
      console.error('删除TAPD需求失败:', error);
      throw error;
    }
  },

  // 清除所有TAPD需求
  async clearAllTapdRequirements() {
    try {
      // 直接使用axios而不是request，绕过拦截器
      const axios = (await import('axios')).default;
      // 使用 getBaseUrl 函数获取正确的基础 URL
      const baseUrl = getBaseUrl();
      const response = await axios.delete(`${baseUrl}/reqAgent/tapd/clear`);
      return response.data;
    } catch (error) {
      console.error('清除所有TAPD需求失败:', error);
      throw error;
    }
  }
}
