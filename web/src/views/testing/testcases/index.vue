<template>
  <!-- 业务页面 -->
  <CommonPage show-footer title="用例列表" class="overflow-auto">
    <template #action>
      <div class="action-buttons">
        <NButton
          class="float-right mr-15"
          type="info"
          @click="exportTestCases"
        >
          <TheIcon icon="material-symbols:download" :size="18" class="mr-5" />导出
        </NButton>
        <NButton
          v-permission="'post/api/v1/testcase/create'"
          class="float-right mr-15"
          type="primary"
          @click="addCases"
        >
          <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新增用例
        </NButton>
      </div>
    </template>

    <!-- 滚动到底部按钮 -->
    <div class="scroll-to-bottom-btn" @click="scrollToBottom(true)">
      <TheIcon icon="mdi:chevron-double-down" :size="20" />
    </div>

    <!-- 返回顶部按钮 -->
    <div class="scroll-to-top-btn" @click="scrollToTop()">
      <TheIcon icon="mdi:chevron-double-up" :size="20" />
    </div>

    <!-- 分页控件 - 位于项目名称下方 -->
    <div class="pagination-container">
      <NPagination
        v-model:page="topPagination.page"
        v-model:page-size="topPagination.pageSize"
        :page-count="topPagination.pageCount"
        :item-count="topPagination.itemCount"
        :page-sizes="topPagination.pageSizes"
        :show-size-picker="topPagination.showSizePicker"
        :prefix="topPagination.prefix"
        @update:page="onTopPageChange"
        @update:page-size="onTopPageSizeChange"
        class="relocated-pagination"
      />
    </div>

    <!-- 表格 -->
    <div class="table-container" style="min-height: calc(100vh - 280px); max-height: calc(100vh - 210px); overflow: auto; position: relative;">
      <CrudTable
        ref="$table"
        v-model:query-items="queryItems"
        :columns="columns"
        :get-data="api.getTestCases"
        :scroll-x="1500"
        :scroll-y="800"
        style="width: 100%; height: 100%;"
        :is-pagination="true"
        class="main-table"
        @onDataChange="onTableDataChange"
      >
      <template #queryBar>
        <QueryBarItem label="项目名称" :label-width="80">
          <NTreeSelect
            v-model:value="queryItems.project_id"
            :options="projOption"
            key-field="key"
            label-field="label"
            value-field="value"
            style="width: 200px;"
            placeholder="请选择项目"
            clearable
            default-expand-all
            :disabled="isDisabled"
            @keypress.enter="$table?.handleSearch()"
          ></NTreeSelect>
        </QueryBarItem>
        <QueryBarItem label="需求名称" :label-width="80">
          <NInput
            v-model:value="queryItems.requirement_keyword"
            style="width: 200px;"
            placeholder="请输入需求名称或TAPD链接"
            clearable
            @keypress.enter="$table?.handleSearch()"
          ></NInput>
        </QueryBarItem>
        <QueryBarItem label="创建者" :label-width="80">
          <NInput
            v-model:value="queryItems.creator"
            style="width: 200px;"
            placeholder="请输入创建者名称"
            clearable
            @keypress.enter="$table?.handleSearch()"
          ></NInput>
        </QueryBarItem>
      </template>
      </CrudTable>
    </div>

    <!-- 新增/编辑 弹窗 -->
<CrudModal
  v-model:visible="modalVisible"
  :title="modalTitle"
  :loading="modalLoading"
  :width="'1200px'"
  @save="handleSave"
>
  <NForm
    ref="modalFormRef"
    label-placement="left"
    label-align="left"
    :label-width="120"
    :model="modalForm"
    :rules="caseRules"
    class="testcase-edit-form"
  >
    <NFormItem label="关联项目" path="project_id">
      <NTreeSelect
        v-model:value="modalForm.project_id"
        :options="projOption"
        key-field="key"
        label-field="label"
        value-field="value"
        placeholder="请选择项目"
        clearable
        default-expand-all
        :disabled="isDisabled"
        @update:value="loadRequirementsByProject"
      ></NTreeSelect>
    </NFormItem>
    <NFormItem label="关联需求" path="requirement_id">
      <NTreeSelect
        v-model:value="modalForm.requirement_id"
        :options="reqsOption"
        key-field="key"
        label-field="label"
        value-field="value"
        placeholder="请选择需求"
        clearable
        default-expand-all
        :disabled="isDisabled || !modalForm.project_id"
      ></NTreeSelect>
    </NFormItem>
    <NFormItem label="用例标题" path="title">
      <NInput
        v-model:value="modalForm.title"
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 4 }"
        clearable
        placeholder="请输入用例标题"
      />
    </NFormItem>
    <NFormItem label="用例描述" path="desc">
      <NInput
        v-model:value="modalForm.desc"
        type="textarea"
        :autosize="{ minRows: 3, maxRows: 6 }"
        clearable
        placeholder="请输入用例描述"
      />
    </NFormItem>
    <NFormItem label="前置条件" path="preconditions">
      <NInput
        v-model:value="modalForm.preconditions"
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 4 }"
        clearable
        placeholder="请输入前置条件"
      />
    </NFormItem>
    <NFormItem label="用例步骤" path="steps" class="test-steps-container">
      <div class="test-steps-list">
        <div v-for="(step, index) in modalForm.steps" :key="index" class="test-step-row">
          <div class="step-number">{{ index + 1 }}.</div>
          <div class="step-operation">
            <NInput
              v-model:value="step.description"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入操作步骤"
            />
          </div>
          <div class="step-connector">
            <TheIcon icon="material-symbols:attach-file" :size="16" />
          </div>
          <div class="step-expected">
            <NInput
              v-model:value="step.expected_result"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入预期结果"
            />
          </div>
          <div class="step-actions">
            <NButton quaternary circle size="small" @click="removeStep(index)" v-if="modalForm.steps.length > 1">
              <TheIcon icon="material-symbols:delete-outline" :size="16" />
            </NButton>
          </div>
        </div>
        <div class="add-step-row">
          <NButton type="primary" @click="addStep" class="add-step-btn">
            <TheIcon icon="material-symbols:add" :size="16" class="mr-5" />新增步骤
          </NButton>
        </div>
      </div>
    </NFormItem>
    <NFormItem label="后置条件" path="postconditions">
      <NInput
        v-model:value="modalForm.postconditions"
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 4 }"
        clearable
        placeholder="请输入后置条件"
      />
    </NFormItem>
    <NFormItem label="优先级" path="priority">
      <NSelect
        v-model:value="modalForm.priority"
        :options="[
          { label: '高', value: '高' },
          { label: '中', value: '中' },
          { label: '低', value: '低' },
        ]"
        placeholder="请选择优先级"
      />
    </NFormItem>
    <NFormItem label="用例状态" path="status">
      <NSelect
        v-model:value="modalForm.status"
        :options="[
          { label: '未开始', value: '未开始' },
          { label: '进行中', value: '进行中' },
          { label: '通过', value: '通过' },
          { label: '失败', value: '失败' },
          { label: '阻塞', value: '阻塞' },
        ]"
        placeholder="请选择状态"
      />
    </NFormItem>
    <NFormItem label="TAPD需求URL" path="tapd_url">
      <NInput
        v-model:value="modalForm.tapd_url"
        type="textarea"
        :autosize="{ minRows: 1, maxRows: 3 }"
        clearable
        placeholder="请输入TAPD需求URL"
      />
    </NFormItem>

    <NFormItem label="用例标签" path="tags">
      <NSelect
        v-model:value="modalForm.tags"
        :options="[
          { label: '单元测试', value: '单元测试' },
          { label: '功能测试', value: '功能测试' },
          { label: '集成测试', value: '集成测试' },
          { label: '系统测试', value: '系统测试' },
          { label: '冒烟测试', value: '冒烟测试' },
          { label: '版本验证', value: '版本验证' },
          { label: '性能测试', value: '性能测试' },
          { label: '压力测试', value: '压力测试' },
          { label: '异常测试', value: '异常测试' },
          { label: '并发测试', value: '并发测试' },
          { label: '边界测试', value: '边界测试' },
          { label: '兼容性测试', value: '兼容性测试' },
          { label: '安全测试', value: '安全测试' },
          { label: 'UI测试', value: 'UI测试' },
          { label: '配置测试', value: '配置测试' },
        ]"
        placeholder="请选择标签"
      />
    </NFormItem>
    <NFormItem label="创建者" path="creator">
      <NInput v-model:value="modalForm.creator" clearable placeholder="请输入创建者" />
    </NFormItem>
    <NFormItem label="采纳状态" path="is_adopted">
      <NSelect
        v-model:value="modalForm.is_adopted"
        :options="[
          { label: '已采纳', value: true },
          { label: '未采纳', value: false },
        ]"
        placeholder="请选择采纳状态"
      />
    </NFormItem>
  </NForm>
</CrudModal>
  </CommonPage>
</template>

<script setup>
import { h, onMounted, onUnmounted, ref, resolveDirective, withDirectives, watch, nextTick } from 'vue'
import { NButton, NForm, NFormItem, NInput, NInputNumber, NPagination, NPopconfirm, NSelect, NStep, NTreeSelect } from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
// import { loginTypeMap, loginTypeOptions } from '@/constant/data'
import api from '@/api'

defineOptions({ name: '用例管理' })

const $table = ref(null)
const queryItems = ref({
  requirement_keyword: '',
  project_id: null,
  creator: '',
})
const vPermission = resolveDirective('permission')

const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleSave,
  modalForm,
  modalFormRef,
  handleEdit,
  handleDelete,
  handleAdd,
} = useCRUD({
  name: '用例',
  initForm: { order: 0 },
  doCreate: api.createTestCase,
  doUpdate: api.updateTestCase,
  doDelete: api.deleteTestCase,
  refresh: () => $table.value?.handleSearch(),
})

const projOption = ref([])
const reqsOption = ref([])
const isDisabled = ref(false)

// 顶部分页状态管理
const topPagination = ref({
  page: 1,
  pageSize: 10,
  pageCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  itemCount: 0,
  prefix({ itemCount }) {
    return `共 ${itemCount} 条`
  }
})

// 顶部分页事件处理函数
const onTopPageChange = (page) => {
  console.log('顶部分页页码变更:', page)
  // 同步更新顶部分页状态
  topPagination.value.page = page

  // 使用CrudTable暴露的updatePagination方法来同步分页状态
  if ($table.value && $table.value.updatePagination) {
    $table.value.updatePagination({
      page: page
    })
  } else if ($table.value && $table.value.pagination) {
    // 备用方案：直接更新分页对象
    $table.value.pagination.page = page
    $table.value.handleQuery()
  } else {
    console.error('无法访问CrudTable的分页方法')
  }
}

const onTopPageSizeChange = (pageSize) => {
  console.log('顶部分页每页数量变更:', pageSize)
  // 同步更新顶部分页状态
  topPagination.value.pageSize = pageSize
  topPagination.value.page = 1 // 切换每页数量时回到第一页

  // 使用CrudTable暴露的updatePagination方法来同步分页状态
  if ($table.value && $table.value.updatePagination) {
    $table.value.updatePagination({
      pageSize: pageSize,
      page: 1
    })
  } else if ($table.value && $table.value.pagination) {
    // 备用方案：直接更新分页对象
    $table.value.pagination.pageSize = pageSize
    $table.value.pagination.page = 1
    $table.value.handleQuery()
  } else {
    console.error('无法访问CrudTable的分页方法')
  }
}

// 监听CrudTable的数据变化，同步更新顶部分页状态
const syncTopPagination = () => {
  if ($table.value && $table.value.pagination) {
    const bottomPagination = $table.value.pagination
    // 只有当分页状态真正发生变化时才更新，避免无限循环
    if (topPagination.value.page !== bottomPagination.page ||
        topPagination.value.pageSize !== bottomPagination.pageSize ||
        topPagination.value.pageCount !== bottomPagination.pageCount ||
        topPagination.value.itemCount !== bottomPagination.itemCount) {

      console.log('同步分页状态:', {
        from: bottomPagination,
        to: topPagination.value
      })

      topPagination.value.page = bottomPagination.page
      topPagination.value.pageSize = bottomPagination.pageSize
      topPagination.value.pageCount = bottomPagination.pageCount
      topPagination.value.itemCount = bottomPagination.itemCount
    }
  }
}

// 表格数据变化事件处理函数
const onTableDataChange = (data) => {
  console.log('表格数据变化事件触发:', data)
  // 当表格数据变化时，同步更新顶部分页状态
  nextTick(() => {
    syncTopPagination()
  })
}

// 添加滚动到顶部的函数
const scrollToTop = () => {
  console.log('执行滚动到顶部');

  try {
    // 首先强制滚动整个页面到顶部
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  } catch (e) {
    console.error('窗口滚动异常:', e);
    // 备用方法
    window.scrollTo(0, 0);
  }

  // 滚动页面容器到顶部
  const pageContainer = document.querySelector('.page-container');
  if (pageContainer) {
    try {
      pageContainer.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    } catch (e) {
      console.error('页面容器滚动异常:', e);
      // 备用方法
      pageContainer.scrollTop = 0;
    }
  }

  // 滚动各个容器到顶部
  nextTick(() => {
    // 尝试滚动表格容器
    const tableContainer = document.querySelector('.table-container');
    if (tableContainer) {
      try {
        tableContainer.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('表格容器滚动异常:', e);
        tableContainer.scrollTop = 0;
      }
    }

    // 尝试滚动表格内容
    const tableBody = document.querySelector('.n-data-table-base-table-body');
    if (tableBody) {
      try {
        tableBody.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('表格内容滚动异常:', e);
        tableBody.scrollTop = 0;
      }
    }

    // 尝试滚动所有可能的滚动容器
    const scrollContainers = [
      '.n-scrollbar-container',
      '.n-data-table-wrapper',
      '.n-data-table',
      '.crud-table-container'
    ];

    scrollContainers.forEach(selector => {
      const container = document.querySelector(selector);
      if (container) {
        try {
          container.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        } catch (e) {
          console.error(`${selector} 滚动异常:`, e);
          container.scrollTop = 0;
        }
      }
    });

    // 显示成功消息
    window.$message?.success('已滚动到顶部');
  });
};

// 添加滚动到底部的函数
const scrollToBottom = (force = false) => {
  console.log('执行滚动到底部，force =', force);

  // 首先尝试滚动整个页面容器
  if (force) {
    try {
      // 滚动整个页面
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: 'smooth'
      });
    } catch (e) {
      console.error('窗口滚动异常:', e);
      // 备用方法
      window.scrollTo(0, document.body.scrollHeight);
    }

    // 滚动页面容器
    const pageContainer = document.querySelector('.page-container');
    if (pageContainer) {
      try {
        pageContainer.scrollTo({
          top: pageContainer.scrollHeight,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('页面容器滚动异常:', e);
        // 备用方法
        pageContainer.scrollTop = pageContainer.scrollHeight;
      }
    }
  }

  nextTick(() => {
    // 尝试滚动表格容器
    const tableContainer = document.querySelector('.table-container');
    if (tableContainer) {
      try {
        tableContainer.scrollTo({
          top: tableContainer.scrollHeight,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('表格容器滚动异常:', e);
        tableContainer.scrollTop = tableContainer.scrollHeight;
      }
    }

    // 尝试滚动表格内容
    const tableBody = document.querySelector('.n-data-table-base-table-body');
    if (tableBody) {
      try {
        tableBody.scrollTo({
          top: tableBody.scrollHeight,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('表格内容滚动异常:', e);
        tableBody.scrollTop = tableBody.scrollHeight;
      }
    }

    // 尝试滚动所有可能的滚动容器
    const scrollContainers = [
      '.n-scrollbar-container',
      '.n-data-table-wrapper',
      '.n-data-table',
      '.crud-table-container'
    ];

    scrollContainers.forEach(selector => {
      const container = document.querySelector(selector);
      if (container) {
        try {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
          });
        } catch (e) {
          console.error(`${selector} 滚动异常:`, e);
          container.scrollTop = container.scrollHeight;
        }
      }
    });

    // 显示成功消息
    window.$message?.success('已滚动到底部');
  });
};

onMounted(() => {
  api.getProjts({ page: 1, page_size: 1000 }).then((res) => {
    if (res.data && Array.isArray(res.data)) {
      projOption.value = res.data.map(item => ({
        key: item.id,
        label: item.name,
        value: Number(item.id)  // [!code ++] 强制转为数字类型
      }));
    }
  });
  $table.value?.handleSearch()

  // 设置定时器定期同步分页状态
  const syncInterval = setInterval(() => {
    syncTopPagination()
  }, 500)

  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(syncInterval)
  })
})

// 根据项目 ID 加载需求列表
const loadRequirementsByProject = async (projectId) => {
  console.log('\n🔍 ===== 开始调试需求列表加载 =====');
  console.log('📋 输入参数 - 项目ID:', projectId, '类型:', typeof projectId);

  if (typeof projectId !== 'number' || isNaN(projectId)) {
    console.error('❌ 无效的项目ID:', projectId);
    reqsOption.value = [];
    return;
  }

  if (!projectId) {
    console.log('⚠️ 项目ID为空，清空需求列表');
    reqsOption.value = [];
    return;
  }

  try {
    console.log('🌐 发起API请求 - getRequirementsList');
    console.log('📤 请求参数:', { project_id: projectId });

    const res = await api.getRequirementsList({ project_id: projectId });

    console.log('📥 API响应原始数据:');
    console.log('- 响应状态:', res.status || 'unknown');
    console.log('- 响应数据:', res);
    console.log('- 数据数组长度:', res.data ? res.data.length : 'N/A');

    if (res.data && Array.isArray(res.data)) {
      console.log('📊 需求列表详细信息:');
      res.data.forEach((item, index) => {
        console.log(`  ${index + 1}. ID: ${item.id}, 名称: "${item.name}", 项目ID: ${item.project_id}`);
      });

      // 设置选项列表，确保数据类型一致
      reqsOption.value = res.data.map(item => ({
        key: Number(item.id),
        label: item.name || `需求${item.id}`,
        value: Number(item.id), // 确保value是数字类型
        tapd_url: item.tapd_url || null,
        // 保存原始数据用于调试
        _raw: item
      }));

      console.log('✅ 处理后的需求选项列表:');
      reqsOption.value.forEach((option, index) => {
        console.log(`  ${index + 1}. value: ${option.value}, label: "${option.label}"`);
      });

      // 监听需求选择变化，自动填充TAPD URL（只设置一次监听器）
      if (!window._requirementWatcherSet) {
        watch(
          () => modalForm.value.requirement_id,
          (newReqId) => {
            if (newReqId) {
              // 查找选中的需求
              const selectedReq = reqsOption.value.find(req =>
                Number(req.value) === Number(newReqId)
              );
              if (selectedReq && selectedReq.tapd_url) {
                // 自动填充TAPD URL
                modalForm.value.tapd_url = selectedReq.tapd_url;
                console.log('🔗 自动填充TAPD URL:', selectedReq.tapd_url);
              }
            }
          }
        );
        window._requirementWatcherSet = true;
      }
    } else {
      console.warn('⚠️ 需求列表数据格式不正确:', res);
      console.log('- res.data 类型:', typeof res.data);
      console.log('- res.data 值:', res.data);
      reqsOption.value = [];
    }

    console.log('🏁 需求列表加载完成，最终选项数量:', reqsOption.value.length);
    console.log('🔍 ===== 需求列表加载调试结束 =====\n');

  } catch (error) {
    console.error('❌ 加载需求失败:', error);
    console.log('📋 错误详情:');
    console.log('- 错误消息:', error.message);
    console.log('- 错误堆栈:', error.stack);
    console.log('- 响应数据:', error.response?.data);
    console.log('- 响应状态:', error.response?.status);

    reqsOption.value = [];
    window.$message?.error('加载需求列表失败，请重试');
    console.log('🔍 ===== 需求列表加载调试结束(错误) =====\n');
  }
}

// 辅助函数：设置需求ID并验证
const setRequirementIdSafely = (targetRequirementId, row, requirementInfo = null) => {
  console.log('\n🔧 ===== 开始设置需求ID =====');
  console.log('🎯 目标需求ID:', targetRequirementId, '类型:', typeof targetRequirementId);
  console.log('📝 需求信息:', requirementInfo);
  console.log('📋 当前需求选项列表长度:', reqsOption.value.length);

  if (!targetRequirementId) {
    console.log('⚠️ 没有需求ID需要设置');
    return false;
  }

  // 验证需求ID是否在选项列表中
  const requirementExists = reqsOption.value.find(req =>
    Number(req.value) === Number(targetRequirementId) ||
    req.value === targetRequirementId ||
    String(req.value) === String(targetRequirementId)
  );

  if (requirementExists) {
    modalForm.value.requirement_id = Number(targetRequirementId);
    console.log('✅ 成功设置需求ID:', targetRequirementId, '匹配的需求:', requirementExists);
    return true;
  } else {
    console.warn('❌ 需求ID不在选项列表中:', targetRequirementId);
    console.log('📋 可用的需求选项:');
    reqsOption.value.forEach((req, index) => {
      console.log(`  ${index + 1}. ID: ${req.value}, 名称: "${req.label}"`);
    });

    // 如果需求不在选项列表中，但测试用例有需求信息，直接添加到选项列表
    if (requirementInfo && requirementInfo.name) {
      console.log('🔧 尝试从测试用例数据中添加需求选项');

      // 将当前需求添加到选项列表
      const newRequirementOption = {
        key: Number(targetRequirementId),
        label: requirementInfo.name,
        value: Number(targetRequirementId),
        tapd_url: requirementInfo.tapd_url || null,
        _fromTestCase: true // 标记这是从测试用例数据中添加的
      };

      reqsOption.value.push(newRequirementOption);
      modalForm.value.requirement_id = Number(targetRequirementId);

      console.log('✅ 成功添加并设置需求ID:', targetRequirementId, '需求名称:', requirementInfo.name);
      return true;
    }

    // 尝试通过需求名称匹配（作为最后的备选方案）
    if (row.requirement && row.requirement.name) {
      const requirementByName = reqsOption.value.find(req =>
        req.label === row.requirement.name ||
        req.label.includes(row.requirement.name) ||
        row.requirement.name.includes(req.label)
      );

      if (requirementByName) {
        modalForm.value.requirement_id = Number(requirementByName.value);
        console.log('✅ 通过名称匹配设置需求ID:', requirementByName.value, '需求名称:', requirementByName.label);
        return true;
      }
    }

    console.error('❌ 无法设置需求ID，所有匹配方式都失败了');
    console.log('🔧 ===== 设置需求ID结束(失败) =====\n');
    return false;
  }
}

// 自定义编辑函数
const customHandleEdit = async (row) => {
  console.log('\n🎯 ===== 开始调试测试用例编辑 =====');
  console.log('📋 原始测试用例数据:', row);
  console.log('📊 数据结构分析:');
  console.log('- 用例ID:', row.id);
  console.log('- 项目ID:', row.project_id, '类型:', typeof row.project_id);
  console.log('- 项目对象:', row.project);
  console.log('- 需求ID:', row.requirement_id, '类型:', typeof row.requirement_id);
  console.log('- 需求对象:', row.requirement);

  // 调用原始的 handleEdit 函数
  handleEdit(row);

  // 保存需求ID和需求信息，用于后续设置
  let targetRequirementId = null;
  let requirementInfo = null;

  // 处理需求 ID - 优先从不同字段获取
  if (row.requirement_id) {
    targetRequirementId = Number(row.requirement_id);
  } else if (row.requirement && row.requirement.id) {
    targetRequirementId = Number(row.requirement.id);
  }

  // 提取需求详细信息
  if (row.requirement) {
    requirementInfo = {
      id: row.requirement.id || targetRequirementId,
      name: row.requirement.name,
      tapd_url: row.requirement.tapd_url,
      description: row.requirement.description,
      // 保存完整的需求对象用于调试
      _full: row.requirement
    };
  }

  console.log('🎯 目标需求ID:', targetRequirementId);
  console.log('📝 需求详细信息:', requirementInfo);

  // 处理关联项目和加载需求列表
  let projectId = null;
  if (row.project_id) {
    projectId = Number(row.project_id);
  } else if (row.project && row.project.id) {
    projectId = Number(row.project.id);
    modalForm.value.project_id = projectId;
  }

  console.log('🏗️ 项目ID:', projectId);

  // 如果有项目ID，加载相关需求列表
  if (projectId) {
    try {
      await loadRequirementsByProject(projectId);
      console.log('📋 需求列表加载完成，开始设置需求ID');

      // 等待下一个tick确保DOM更新完成
      await nextTick();

      // 设置需求ID（确保需求列表已加载）
      if (targetRequirementId) {
        const success = setRequirementIdSafely(targetRequirementId, row, requirementInfo);
        if (!success) {
          // 如果设置失败，显示用户友好的提示
          window.$message?.warning(`无法找到对应的需求信息，需求ID: ${targetRequirementId}`);
        }
      }
    } catch (error) {
      console.error('❌ 加载需求列表失败:', error);
    }
  } else {
    // 如果没有项目ID，但有需求信息，尝试直接处理
    if (targetRequirementId) {
      console.log('⚠️ 没有项目ID，尝试直接处理需求信息');

      // 如果测试用例包含需求信息，直接添加到选项列表并设置
      if (row.requirement && row.requirement.name) {
        // 检查是否已经在选项列表中
        const existingReq = reqsOption.value.find(req =>
          Number(req.value) === Number(targetRequirementId)
        );

        if (!existingReq) {
          // 添加到选项列表
          const newRequirementOption = {
            key: Number(targetRequirementId),
            label: row.requirement.name,
            value: Number(targetRequirementId),
            tapd_url: row.requirement.tapd_url || row.tapd_url || null,
            _fromTestCase: true
          };

          reqsOption.value.push(newRequirementOption);
          console.log('✅ 添加需求选项到列表:', newRequirementOption);
        }

        modalForm.value.requirement_id = Number(targetRequirementId);
        console.log('✅ 成功设置需求ID:', targetRequirementId);
      } else {
        // 没有需求详细信息，直接设置ID
        modalForm.value.requirement_id = targetRequirementId;
        console.log('⚠️ 没有需求详细信息，直接设置需求ID:', targetRequirementId);
      }
    }
  }

  // 处理标签
  if (row.tags) {
    modalForm.value.tags = row.tags;
  }

  // 处理测试步骤
  if (row.steps && Array.isArray(row.steps)) {
    modalForm.value.steps = [...row.steps];
  } else if (row.testcase_steps && Array.isArray(row.testcase_steps)) {
    modalForm.value.steps = [...row.testcase_steps];
  } else {
    modalForm.value.steps = [];
  }

  // 处理TAPD URL
  if (row.tapd_url) {
    modalForm.value.tapd_url = row.tapd_url;
  }

  // 处理采纳状态
  if (row.is_adopted !== undefined && row.is_adopted !== null) {
    modalForm.value.is_adopted = row.is_adopted;
  } else {
    // 如果没有采纳状态信息，默认设置为已采纳
    modalForm.value.is_adopted = true;
  }

  console.log('🏁 编辑用例表单数据最终结果:', modalForm.value);
  console.log('📋 当前需求选项列表:', reqsOption.value);
  console.log('🎯 ===== 测试用例编辑调试结束 =====\n');
}
// 监听主查询栏中的项目变化
watch(
  () => queryItems.value.project_id,
  async (newVal) => {
    if (!newVal) {
      reqsOption.value = [];
      // 清空需求ID，但保留需求关键词，因为关键词可以跨项目搜索
      queryItems.value.requirement_id = null;
      return;
    }
    await loadRequirementsByProject(Number(newVal)); // 明确传递数字类型
  }
);

// 不需要额外监听弹窗表单的项目变化，因为已经在NTreeSelect组件上添加了@update:value事件

const caseRules = {
  name: [
    {
      required: true,
      message: '请输入用例名称',
      trigger: ['input', 'blur', 'change'],
    },
  ],
}

async function addCases() {
  isDisabled.value = false
  handleAdd()
  // 初始化测试步骤
  modalForm.value.steps = []
}

// 导出测试用例到Excel
const exportTestCases = async () => {
  try {
    // 显示加载中提示
    window.$message?.loading('正在准备导出数据...')

    // 构建查询参数，与当前筛选条件一致
    const params = {
      page: 1,
      page_size: 1000, // 导出时使用较大的页面大小
      ...queryItems.value
    }

    // 打印查询参数便于调试
    console.log('导出查询参数:', params)

    // 使用API获取数据
    console.log('使用API获取数据导出')

    // 直接调用表格组件的API获取数据
    const response = await api.getTestCases(params)
    console.log('获取到的数据:', response)

    // 检查是否有数据
    if (!response || !response.data || response.data.length === 0) {
      window.$message?.warning('没有数据可导出')
      return
    }

    // 使用获取到的数据
    const tableData = response.data
    console.log('导出数据:', tableData)

    // 使用客户端导出功能
    // 引入xlsx库
    const XLSX = await import('xlsx')

    // 准备数据
    const exportData = tableData.map(item => {
      // 处理测试步骤和预期结果
      let stepsText = ''
      let expectedResultsText = ''

      if (item.steps && Array.isArray(item.steps)) {
        item.steps.forEach((step, index) => {
          stepsText += `${index + 1}. ${step.description || ''}\n`
          expectedResultsText += `${index + 1}. ${step.expected_result || ''}\n`
        })
      }

      return {
        '用例ID': item.id,
        '用例标题': item.title,
        '用例描述': item.desc,
        '前置条件': item.preconditions,
        '测试步骤': stepsText,
        '预期结果': expectedResultsText,
        '后置条件': item.postconditions,
        '优先级': item.priority,
        '状态': item.status,
        '标签': Array.isArray(item.tags) ? item.tags.join(', ') : item.tags,
        '项目名称': item.project_name,
        '需求名称': item.requirement_name,
        'TAPD链接': item.tapd_url,
        '创建者': item.creator,
        '创建时间': item.created_at
      }
    })

    // 创建workbook
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.json_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 8 }, // 用例ID
      { wch: 20 }, // 用例标题
      { wch: 30 }, // 用例描述
      { wch: 20 }, // 前置条件
      { wch: 40 }, // 测试步骤
      { wch: 40 }, // 预期结果
      { wch: 20 }, // 后置条件
      { wch: 10 }, // 优先级
      { wch: 10 }, // 状态
      { wch: 15 }, // 标签
      { wch: 15 }, // 项目名称
      { wch: 20 }, // 需求名称
      { wch: 30 }, // TAPD链接
      { wch: 15 }, // 创建者
      { wch: 20 }  // 创建时间
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '测试用例')

    // 导出文件
    XLSX.writeFile(wb, '测试用例导出.xlsx')

    // 显示成功提示
    window.$message?.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    window.$message?.error(`导出失败: ${error.message || '未知错误'}`)
  }
}

// 添加测试步骤
const addStep = () => {
  if (!modalForm.value.steps) {
    modalForm.value.steps = [];
  }
  modalForm.value.steps.push({
    description: '',
    expected_result: '',
  });
};

// 删除测试步骤
const removeStep = (index) => {
  if (modalForm.value.steps && modalForm.value.steps.length > 1) {
    modalForm.value.steps.splice(index, 1);
  }
};

const columns = [
  {
    title: '用例ID',
    key: 'id',
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
    resizable: true,
    render(row) {
      // 将用例ID渲染为可点击的链接
      return h(
        'a',
        {
          style: 'color: #2080f0; cursor: pointer; text-decoration: underline; font-weight: 500; transition: all 0.2s ease;',
          title: '点击编辑用例',
          onClick: () => {
            // 点击ID时触发编辑功能
            customHandleEdit(row);
          },
          onMouseenter: (e) => {
            e.target.style.color = '#1c7ed6';
            e.target.style.textDecoration = 'underline';
            e.target.style.fontWeight = '600';
          },
          onMouseleave: (e) => {
            e.target.style.color = '#2080f0';
            e.target.style.textDecoration = 'underline';
            e.target.style.fontWeight = '500';
          },
        },
        row.id
      );
    },
  },
  {
    title: '用例标题',
    key: 'title',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
  },
  {
    title: '用例描述',
    key: 'desc',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
  },
  {
    title: '前置条件',
    key: 'preconditions',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
  },
  {
    title: '用例步骤',
    key: 'steps',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
    render(row) {
      // 展示步骤列表
      // 先检查 steps 字段，再检查 testcase_steps 字段
      const steps = row.steps || row.testcase_steps;

      if (!steps) {
        return h('div', {}, '');
      }

      if (Array.isArray(steps)) {
        // 如果是数组，则使用 map 函数
        return h(
          'div',
          {},
          steps.map(step => {
            // 创建带有悬浮提示的段落
            return h(
              'p',
              {
                style: 'cursor: pointer; margin-bottom: 5px;',
                title: step.description, // 设置悬浮提示内容
              },
              // 显示简化的内容，如果超过20个字符则截断
              step.description.length > 20 ? step.description.substring(0, 20) + '...' : step.description
            );
          })
        );
      } else {
        // 如果不是数组，直接显示
        return h('div', {}, JSON.stringify(steps));
      }
    },
  },
  {
    title: '预期结果',
    key: 'expected_results',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
    render(row) {
      // 展示预期结果列表
      // 先检查 steps 字段，再检查 testcase_steps 字段
      const steps = row.steps || row.testcase_steps;

      if (!steps) {
        return h('div', {}, '');
      }

      if (Array.isArray(steps)) {
        // 如果是数组，则使用 map 函数提取预期结果
        return h(
          'div',
          {},
          steps.map(step => {
            // 创建带有悬浮提示的段落
            return h(
              'p',
              {
                style: 'cursor: pointer; margin-bottom: 5px;',
                title: step.expected_result, // 设置悬浮提示内容
              },
              // 显示简化的内容，如果超过20个字符则截断
              step.expected_result && step.expected_result.length > 20
                ? step.expected_result.substring(0, 20) + '...'
                : (step.expected_result || '')
            );
          })
        );
      } else {
        // 如果不是数组，直接显示
        return h('div', {}, '');
      }
    },
  },
  {
    title: '后置条件',
    key: 'postconditions',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
  },
  {
    title: '优先级',
    key: 'priority',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
    render(row) {
      // 根据优先级值显示对应的中文
      if (!row.priority) {
        return h('span', {}, '');
      }

      // 处理不同格式的优先级值
      const priorityMap = { HIGH: '高', MEDIUM: '中', LOW: '低', '高': '高', '中': '中', '低': '低' };
      return h('span', {}, priorityMap[row.priority] || row.priority);
    },
  },
  {
    title: '用例状态',
    key: 'status',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
    render(row) {
      // 根据状态值显示对应的中文
      if (!row.status) {
        return h('span', {}, '');
      }

      // 处理不同格式的状态值
      const statusMap = {
        NOT_STARTED: '未开始',
        IN_PROGRESS: '进行中',
        PASSED: '通过',
        FAILED: '失败',
        BLOCKED: '阻塞',
        '未开始': '未开始',
        '进行中': '进行中',
        '通过': '通过',
        '失败': '失败',
        '阻塞': '阻塞',
      };
      return h('span', {}, statusMap[row.status] || row.status);
    },
  },
  {
    title: '用例标签',
    key: 'tags',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
    render(row) {
      // 展示标签
      // 处理 tags 可能是字符串或数组的情况
      if (!row.tags) {
        return h('div', {}, '');
      }

      if (Array.isArray(row.tags)) {
        // 如果是数组，则使用 map 函数
        return h(
          'div',
          {},
          row.tags.map(tag => h('span', { style: 'margin-right: 5px;' }, tag))
        );
      } else {
        // 如果是字符串或其他类型，直接显示
        return h('div', {}, row.tags.toString());
      }
    },
  },
  {
    title: '关联需求',
    key: 'requirement',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
    render(row) {
      // 展示需求名称
      if (row.requirement && row.requirement.name) {
        return h('div', {}, row.requirement.name);
      } else if (row.requirement_id) {
        return h('div', {}, `需求ID: ${row.requirement_id}`);
      } else {
        return h('div', {}, '');
      }
    },
  },
  {
    title: 'TAPD需求链接',
    key: 'tapd_url',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
    render(row) {
      // 展示TAPD URL链接
      if (row.tapd_url) {
        return h(
          'a',
          {
            href: row.tapd_url,
            target: '_blank',
            style: 'color: #2080f0; text-decoration: underline;'
          },
          'TAPD需求链接'
        );
      } else {
        return h('div', {}, '');
      }
    },
  },
  {
    title: '关联项目',
    key: 'project',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
    render(row) {
      // 展示项目名称
      if (row.project && row.project.name) {
        return h('div', {}, row.project.name);
      } else if (row.project_id) {
        return h('div', {}, `项目ID: ${row.project_id}`);
      } else {
        return h('div', {}, '');
      }
    },
  },
  {
    title: '创建者',
    key: 'creator',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
  },
  {
    title: '创建时间',
    key: 'created_at',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    resizable: true,
    render(row) {
      // 展示创建时间，如果存在则格式化显示
      if (row.created_at) {
        return h('div', {}, row.created_at);
      } else {
        return h('div', {}, '');
      }
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 'auto',
    align: 'center',
    fixed: 'right',
    resizable: true,
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: 'margin-left: 8px;',
              onClick: () => {
                // 调用自定义的编辑函数
                customHandleEdit(row);
              },
            },
            {
              default: () => '编辑',
              icon: renderIcon('material-symbols:edit', { size: 16 }),
            }
          ),
          [[vPermission, 'post/api/v1/testcase/update']]
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ case_id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    style: 'margin-left: 8px;',
                  },
                  {
                    default: () => '删除',
                    icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/testcase/delete']]
              ),
            default: () => h('div', {}, '确定删除该用例吗?'),
          }
        ),
      ];
    },
  },
];
</script>

<style scoped>
/* 新的分页容器样式 - 位于项目名称下方 */
.pagination-container {
  margin: 16px 0;
  padding: 12px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

/* 重新定位的分页控件样式 */
.relocated-pagination {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 10px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  overflow: visible;
}

.table-container {
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
  padding-top: 20px; /* 减少顶部空间，因为分页控件已移除 */
}

.table-container :deep(.n-data-table) {
  max-height: calc(100% - 50px); /* 留出分页区域的空间 */
  display: flex;
  flex-direction: column;
}

.table-container :deep(.n-data-table-base-table-body) {
  overflow: auto !important;
  max-height: calc(100vh - 350px);
}



/* 重新定位分页控件的内部元素样式 */
.relocated-pagination :deep(.n-pagination-item) {
  min-width: 32px;
  height: 32px;
  font-size: 14px;
  margin: 0 3px;
}

.relocated-pagination :deep(.n-pagination-quick-jumper) {
  font-size: 14px;
}

.relocated-pagination :deep(.n-pagination-size-picker) {
  font-size: 14px;
}

.relocated-pagination :deep(.n-pagination-prefix) {
  font-size: 14px;
  color: #374151;
  margin-right: 12px;
  font-weight: 500;
}

/* 响应式调整，屏幕较小时的样式 */
@media (max-width: 768px) {
  .pagination-container {
    margin: 12px 0;
    padding: 10px 16px;
  }

  .relocated-pagination {
    padding: 8px 12px;
    gap: 8px;
    flex-wrap: wrap;
  }

  .relocated-pagination :deep(.n-pagination-item) {
    min-width: 28px;
    height: 28px;
    font-size: 13px;
    margin: 0 2px;
  }
}

/* 在中等屏幕上隐藏前缀文本 */
@media (max-width: 992px) {
  .relocated-pagination :deep(.n-pagination-prefix) {
    display: none; /* 隐藏前缀文本以节省空间 */
  }
}

/* 隐藏小屏幕上的部分元素 */
@media (max-width: 576px) {
  .pagination-container {
    margin: 8px 0;
    padding: 8px 12px;
  }

  .relocated-pagination {
    padding: 6px 10px;
    gap: 6px;
  }

  .relocated-pagination :deep(.n-pagination-size-picker) {
    display: none;
  }
}

/* 调整表格底部的间距，确保最后一行可见 */
.table-container :deep(.n-data-table-wrapper) {
  padding-bottom: 20px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 确保水平和垂直滚动条都可见 */
.table-container :deep(.n-scrollbar) {
  overflow: auto !important;
}

/* 设置滚动条样式 */
.table-container :deep(.n-scrollbar-rail) {
  z-index: 10;
}

/* 表格内容区域滚动 */
.table-container :deep(.n-data-table-base-table-body) {
  max-height: calc(100vh - 350px);
  overflow: auto !important;
}

/* 确保分页区域可见 */
.table-container :deep(.n-data-table-pagination) {
  margin-top: 16px;
  position: relative;
  z-index: 10;
}

/* 确保表格内容区域有足够的空间 */
.table-container :deep(.n-data-table) {
  display: flex;
  flex-direction: column;
}



/* 列宽调整样式 */
.table-container :deep(.n-data-table-th) {
  position: relative;
  transition: background-color 0.3s;
}

/* 调整列宽时的拖动手柄样式 */
.table-container :deep(.n-data-table-resize-button) {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  cursor: col-resize;
  z-index: 1;
}

/* 拖动时的视觉反馈 */
.table-container :deep(.n-data-table-resize-button:hover),
.table-container :deep(.n-data-table-resize-button:active) {
  background-color: #6D28D9;
  opacity: 0.5;
}

/* 固定的滚动按钮 - 底部 */
.scroll-to-bottom-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #2080f0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.scroll-to-bottom-btn:hover {
  bottom: 30px; /* 固定在底部 */
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* 固定的滚动按钮 - 顶部 */
.scroll-to-top-btn {
  position: fixed;
  bottom: 100px; /* 位于底部按钮上方 */
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #18a058; /* 使用不同的颜色区分 */
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.scroll-to-top-btn:hover {
  bottom: 90px;
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* 用例ID链接样式 */
.table-container :deep(.n-data-table-td a) {
  display: inline-block;
  padding: 2px 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.table-container :deep(.n-data-table-td a:hover) {
  background-color: rgba(32, 128, 240, 0.1);
  transform: scale(1.05);
}

/* 测试用例编辑表单样式优化 */
.testcase-edit-form {
  max-width: 100%;
  padding: 0 20px;
}

.testcase-edit-form :deep(.n-form-item) {
  margin-bottom: 24px;
}

.testcase-edit-form :deep(.n-form-item-label) {
  font-weight: 600;
  color: #374151;
  min-width: 120px;
  padding-right: 16px;
}

.testcase-edit-form :deep(.n-input) {
  width: 100%;
  font-size: 14px;
  line-height: 1.5;
}

.testcase-edit-form :deep(.n-input__textarea) {
  min-height: 40px;
  resize: vertical;
  font-family: inherit;
  line-height: 1.6;
  word-wrap: break-word;
  word-break: break-all;
}

.testcase-edit-form :deep(.n-tree-select),
.testcase-edit-form :deep(.n-select) {
  width: 100%;
}

/* 测试步骤容器优化 */
.testcase-edit-form .test-steps-container {
  margin-bottom: 32px;
}

.testcase-edit-form .test-steps-container :deep(.n-form-item-blank) {
  width: 100%;
}

/* 测试步骤行布局优化 */
.testcase-edit-form .test-step-row {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f3f4f6;
  margin-bottom: 16px;
}

.testcase-edit-form .test-step-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.testcase-edit-form .step-number {
  width: 40px;
  font-weight: 600;
  color: #6366f1;
  flex-shrink: 0;
  text-align: center;
  padding-top: 8px;
  font-size: 16px;
}

.testcase-edit-form .step-operation {
  flex: 1;
  min-width: 0;
}

.testcase-edit-form .step-connector {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6366f1;
  width: 40px;
  flex-shrink: 0;
  padding-top: 8px;
}

.testcase-edit-form .step-expected {
  flex: 1;
  min-width: 0;
}

.testcase-edit-form .step-actions {
  width: 40px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  padding-top: 8px;
}

.testcase-edit-form .add-step-row {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  margin-top: 16px;
  border-top: 2px dashed #e5e7eb;
}

.testcase-edit-form .add-step-btn {
  background: linear-gradient(45deg, #6366f1 0%, #8b5cf6 100%);
  border: none;
  color: white;
  font-weight: 500;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.testcase-edit-form .add-step-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .testcase-edit-form {
    padding: 0 16px;
  }

  .testcase-edit-form .test-step-row {
    flex-direction: column;
    gap: 12px;
  }

  .testcase-edit-form .step-number,
  .testcase-edit-form .step-connector,
  .testcase-edit-form .step-actions {
    width: 100%;
    text-align: left;
    padding-top: 0;
  }

  .testcase-edit-form .step-operation,
  .testcase-edit-form .step-expected {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .testcase-edit-form :deep(.n-form-item-label) {
    min-width: 100px;
    padding-right: 12px;
  }
}
</style>


