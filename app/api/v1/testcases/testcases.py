import io
import pandas as pd
from fastapi import APIRouter, Query, HTTPException
from fastapi.responses import StreamingResponse
from app.controllers.testcase import testcase_controller
from app.controllers.requirement import requirement_controller
from app.controllers.project import project_controller
from app.schemas.testcases import *
from app.schemas.base import Success, SuccessExtra
from tortoise.expressions import Q
import logging
from app.api.v1.testcases.db_utils import fix_all_invalid_tags, get_all_testcases_with_invalid_tags

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/create", summary="创建测试用例")
async def create_testcase(
    case_in: CaseCreate,
):
    """
    创建新测试用例。
    """
    try:
        # 使用SQL脚本直接插入数据
        import subprocess
        import tempfile
        import os

        # 创建临时SQL文件
        with tempfile.NamedTemporaryFile(suffix='.sql', delete=False) as temp:
            # 获取最大的 test_case_id 和 id
            get_max_id_sql = """
            SELECT MAX(test_case_id), MAX(id) FROM test_cases;
            """

            # 写入SQL文件
            temp_query = tempfile.NamedTemporaryFile(suffix='.sql', delete=False)
            temp_query.write(get_max_id_sql.encode('utf-8'))
            temp_query.flush()

            # 执行SQL脚本获取最大ID
            cmd_query = f"PGPASSWORD=admin123 psql -h localhost -U admin -d agent_testing -t -f {temp_query.name}"
            result_query = subprocess.run(cmd_query, shell=True, capture_output=True, text=True)

            # 删除临时文件
            os.unlink(temp_query.name)

            # 解析结果
            if result_query.returncode == 0 and result_query.stdout.strip():
                parts = result_query.stdout.strip().split('|')
                max_test_case_id = int(parts[0].strip() or 0)
                max_id = int(parts[1].strip() or 0)
                next_test_case_id = max_test_case_id + 1
                next_id = max_id + 1
            else:
                next_test_case_id = 1
                next_id = 1

            logger.info(f"Using test_case_id: {next_test_case_id}, id: {next_id}")

            # 构建SQL插入语句
            sql = f"""
            -- Insert a test case with explicit ID
            INSERT INTO test_cases (
                id, test_case_id, title, "desc", priority, status, preconditions, postconditions,
                tags, requirement_id, project_id, creator, tapd_url, created_at, updated_at
            ) VALUES (
                {next_id}, {next_test_case_id}, '{case_in.title}', '{case_in.desc}', '{case_in.priority.value}', '{case_in.status.value}',
                '{case_in.preconditions}', '{case_in.postconditions}', '{case_in.tags.value}',
                {case_in.requirement_id}, {case_in.project_id}, '{case_in.creator}', '{case_in.tapd_url or ""}', NOW(), NOW()
            );
            """

            # 获取最大的 test_step_id
            get_max_step_id_sql = """
            SELECT MAX(id) FROM test_steps;
            """

            # 写入SQL文件
            temp_step_query = tempfile.NamedTemporaryFile(suffix='.sql', delete=False)
            temp_step_query.write(get_max_step_id_sql.encode('utf-8'))
            temp_step_query.flush()

            # 执行SQL脚本获取最大步骤ID
            cmd_step_query = f"PGPASSWORD=admin123 psql -h localhost -U admin -d agent_testing -t -f {temp_step_query.name}"
            result_step_query = subprocess.run(cmd_step_query, shell=True, capture_output=True, text=True)

            # 删除临时文件
            os.unlink(temp_step_query.name)

            # 解析结果
            if result_step_query.returncode == 0 and result_step_query.stdout.strip():
                max_step_id = int(result_step_query.stdout.strip() or 0)
                next_step_id = max_step_id + 1
            else:
                next_step_id = 1

            logger.info(f"Using next_step_id: {next_step_id}")

            # 如果有测试步骤，添加测试步骤
            if hasattr(case_in, 'steps') and case_in.steps:
                for i, step in enumerate(case_in.steps):
                    step_id = next_step_id + i
                    sql += f"""
                    -- Insert a test step with explicit ID
                    INSERT INTO test_steps (
                        id, step_id, test_case_id, description, expected_result, created_at, updated_at
                    ) VALUES (
                        {step_id}, {i+1}, {next_id}, '{step.description}', '{step.expected_result}', NOW(), NOW()
                    );

                    -- Insert the many-to-many relationship
                    INSERT INTO test_cases_test_steps (
                        test_cases_id, teststep_id
                    ) VALUES (
                        {next_id}, {step_id}
                    );
                    """

            # 写入SQL文件
            temp.write(sql.encode('utf-8'))
            temp.flush()

            # 执行SQL脚本
            cmd = f"PGPASSWORD=admin123 psql -h localhost -U admin -d agent_testing -f {temp.name}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                logger.error(f"Error executing SQL: {result.stderr}")
                raise Exception(f"Error executing SQL: {result.stderr}")

            logger.info(f"SQL execution result: {result.stdout}")

            # 删除临时文件
            os.unlink(temp.name)

        return Success(msg="Created Successfully")
    except Exception as e:
        logger.error(f"Error creating test case: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建测试用例失败: {str(e)}")


@router.get("/list", summary="查看测试用例列表")
async def list_testcase(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    project_id: int = Query(None, description="项目名称，用于查询"),
    requirement_id: int = Query(None, description="需求ID，用于查询"),
    requirement_keyword: str = Query(None, description="需求名称或TAPD链接关键词"),
    creator: str = Query(None, description="创建者名称"),
):
    """
    获取测试用例列表。
    支持按项目、需求ID、需求名称、TAPD链接或创建者进行查询。
    """
    q = Q()
    if project_id is not None:
        q &= Q(project_id=project_id)
    if requirement_id is not None:
        q &= Q(requirement_id=requirement_id)

    # 如果提供了需求关键词，则查询相关需求的测试用例
    if requirement_keyword:
        # 首先查询匹配关键词的需求
        req_query = Q(name__icontains=requirement_keyword) | Q(tapd_url__icontains=requirement_keyword)
        matching_reqs = await requirement_controller.model.filter(req_query).values('id')
        matching_req_ids = [req['id'] for req in matching_reqs]

        if matching_req_ids:
            # 如果找到匹配的需求，则查询这些需求的测试用例
            q &= Q(requirement_id__in=matching_req_ids)
        else:
            # 如果没有找到匹配的需求，则直接查询测试用例的TAPD URL
            q &= Q(tapd_url__icontains=requirement_keyword)

    # 如果提供了创建者名称，则查询该创建者的测试用例
    if creator:
        q &= Q(creator__icontains=creator)

    # 调用控制器的 list 方法
    total, testcase_objs = await testcase_controller.list(page=page, page_size=page_size, search=q)

    # 将结果转换为字典格式，并处理外键字段
    data = [await obj.to_dict(m2m=True) for obj in testcase_objs]
    for item in data:
        # 替换 project_id 为项目详情
        project_id = item.pop("project_id", None)
        try:
            if project_id:
                project = await project_controller.get(id=project_id)
                item["project"] = await project.to_dict()
            else:
                item["project"] = {}
        except Exception as e:
            logger.error(f"Error fetching project with id {project_id}: {str(e)}")
            item["project"] = {}

        # 替换 requirement_id 为需求详情
        requirement_id = item.pop("requirement_id", None)
        try:
            if requirement_id:
                requirement = await requirement_controller.get(id=requirement_id)
                item["requirement"] = await requirement.to_dict()
            else:
                item["requirement"] = {}
        except Exception as e:
            logger.error(f"Error fetching requirement with id {requirement_id}: {str(e)}")
            item["requirement"] = {}

    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="查看测试用例详情")
async def get_testcase(
    id: str = Query(..., description="用例ID"),
):
    """
    获取单个用例详情。
    """
    case_obj = await testcase_controller.get(id=id)
    return Success(data=await case_obj.to_dict())


@router.put("/update", summary="更新用例")
async def update_testcase(
    case_in: CaseUpdate
):
    """
    更新需求信息。
    """
    try:
        # 使用SQL脚本直接更新数据
        import subprocess
        import tempfile
        import os

        # 创建临时SQL文件
        with tempfile.NamedTemporaryFile(suffix='.sql', delete=False) as temp:
            # 构建SQL更新语句
            sql = f"""
            -- Update test case
            UPDATE test_cases
            SET
                title = '{case_in.title or ""}',
                "desc" = '{case_in.desc or ""}',
                priority = '{case_in.priority.value if case_in.priority else "高"}',
                status = '{case_in.status.value if case_in.status else "未开始"}',
                preconditions = '{case_in.preconditions or ""}',
                postconditions = '{case_in.postconditions or ""}',
                tags = '{case_in.tags.value if case_in.tags else "单元测试"}',
                requirement_id = {case_in.requirement_id or 1},
                project_id = {case_in.project_id or 1},
                creator = '{case_in.creator or ""}',
                tapd_url = '{case_in.tapd_url or ""}',
                updated_at = NOW()
            WHERE id = {case_in.id};
            """

            # 如果有测试步骤，先删除旧的步骤，再创建新的步骤
            if hasattr(case_in, 'steps') and case_in.steps:
                sql += f"""
                -- Delete old test steps
                DELETE FROM test_steps WHERE test_case_id = {case_in.id};

                -- Delete old relationships
                DELETE FROM test_cases_test_steps WHERE test_cases_id = {case_in.id};
                """

                # 获取最大的 test_step_id
                get_max_step_id_sql = """
                SELECT MAX(id) FROM test_steps;
                """

                # 写入SQL文件
                temp_step_query = tempfile.NamedTemporaryFile(suffix='.sql', delete=False)
                temp_step_query.write(get_max_step_id_sql.encode('utf-8'))
                temp_step_query.flush()

                # 执行SQL脚本获取最大步骤ID
                cmd_step_query = f"PGPASSWORD=admin123 psql -h localhost -U admin -d agent_testing -t -f {temp_step_query.name}"
                result_step_query = subprocess.run(cmd_step_query, shell=True, capture_output=True, text=True)

                # 删除临时文件
                os.unlink(temp_step_query.name)

                # 解析结果
                if result_step_query.returncode == 0 and result_step_query.stdout.strip():
                    max_step_id = int(result_step_query.stdout.strip() or 0)
                    next_step_id = max_step_id + 1
                else:
                    next_step_id = 1

                logger.info(f"Using next_step_id for update: {next_step_id}")

                # 添加新的测试步骤
                for i, step in enumerate(case_in.steps):
                    step_id = next_step_id + i
                    sql += f"""
                    -- Insert new test step
                    INSERT INTO test_steps (
                        id, step_id, test_case_id, description, expected_result, created_at, updated_at
                    ) VALUES (
                        {step_id}, {i+1}, {case_in.id}, '{step.description}', '{step.expected_result}', NOW(), NOW()
                    );

                    -- Insert new relationship
                    INSERT INTO test_cases_test_steps (
                        test_cases_id, teststep_id
                    ) VALUES (
                        {case_in.id}, {step_id}
                    );
                    """

            # 写入SQL文件
            temp.write(sql.encode('utf-8'))
            temp.flush()

            # 执行SQL脚本
            cmd = f"PGPASSWORD=admin123 psql -h localhost -U admin -d agent_testing -f {temp.name}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                logger.error(f"Error executing SQL: {result.stderr}")
                raise Exception(f"Error executing SQL: {result.stderr}")

            logger.info(f"SQL execution result: {result.stdout}")

            # 删除临时文件
            os.unlink(temp.name)

        return Success(msg="Update Successfully")
    except Exception as e:
        logger.error(f"Error updating test case: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新测试用例失败: {str(e)}")


@router.delete("/delete", summary="删除用例")
async def delete_testcase(
    case_id: int = Query(..., description="用例ID")
):
    """
    删除测试用例。
    """
    await testcase_controller.delete_TestCase(case_id=case_id)
    return Success(msg="Deleted Successfully")


@router.get("/fix-tags", summary="修复测试用例标签")
async def fix_testcase_tags():
    """
    修复所有标签值不在枚举范围内的测试用例。
    """
    result = await fix_all_invalid_tags()
    return SuccessExtra(data=result, msg="Fix Tags Successfully")


@router.get("/invalid-tags", summary="获取无效标签的测试用例")
async def get_invalid_testcase_tags():
    """
    获取所有标签值不在枚举范围内的测试用例。
    """
    invalid_testcases = await get_all_testcases_with_invalid_tags()
    return SuccessExtra(data=invalid_testcases, msg="Get Invalid Tags Successfully")


@router.put("/update-adoption", summary="更新测试用例采纳状态")
async def update_testcase_adoption(
    case_id: int = Query(..., description="测试用例ID"),
    is_adopted: bool = Query(..., description="是否采纳")
):
    """
    更新测试用例的采纳状态。
    """
    try:
        await testcase_controller.update_adoption_status(case_id=case_id, is_adopted=is_adopted)
        return Success(msg="更新采纳状态成功")
    except Exception as e:
        logger.error(f"更新测试用例采纳状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新测试用例采纳状态失败: {str(e)}")


@router.get("/export/excel", summary="导出测试用例到Excel", response_class=StreamingResponse)
async def export_testcases_to_excel(
    page: int = Query(1, description="页码"),
    page_size: int = Query(1000, description="每页数量，导出时默认较大"),
    project_id: int = Query(None, description="项目名称，用于查询"),
    requirement_id: int = Query(None, description="需求ID，用于查询"),
    requirement_keyword: str = Query(None, description="需求名称或TAPD链接关键词"),
    creator: str = Query(None, description="创建者名称"),
):
    """
    导出测试用例到Excel文件。
    支持按项目、需求ID、需求名称、TAPD链接或创建者进行筛选。
    """
    try:
        # 构建查询条件，与list_testcase相同
        q = Q()
        if project_id is not None:
            q &= Q(project_id=project_id)
        if requirement_id is not None:
            q &= Q(requirement_id=requirement_id)

        # 如果提供了需求关键词，则查询相关需求的测试用例
        if requirement_keyword:
            # 首先查询匹配关键词的需求
            req_query = Q(name__icontains=requirement_keyword) | Q(tapd_url__icontains=requirement_keyword)
            matching_reqs = await requirement_controller.model.filter(req_query).values('id')
            matching_req_ids = [req['id'] for req in matching_reqs]

            if matching_req_ids:
                # 如果找到匹配的需求，则查询这些需求的测试用例
                q &= Q(requirement_id__in=matching_req_ids)
            else:
                # 如果没有找到匹配的需求，则直接查询测试用例的TAPD URL
                q &= Q(tapd_url__icontains=requirement_keyword)

        # 如果提供了创建者名称，则查询该创建者的测试用例
        if creator:
            q &= Q(creator__icontains=creator)

        # 调用控制器的 list 方法
        result = await testcase_controller.list(page=page, page_size=page_size, search=q)
        total = result[0]  # 总数
        testcase_objs = result[1]  # 测试用例对象列表

        # 处理数据，与list_testcase相同
        data = []
        for obj in testcase_objs:
            item = await obj.to_dict()

            # 替换 project_id 为项目名称
            project_id = item.pop("project_id", None)
            try:
                if project_id:
                    project = await project_controller.get(id=project_id)
                    item["project_name"] = project.name
                else:
                    item["project_name"] = ""
            except Exception as e:
                logger.error(f"Error fetching project with id {project_id}: {str(e)}")
                item["project_name"] = ""

            # 替换 requirement_id 为需求详情
            requirement_id = item.pop("requirement_id", None)
            try:
                if requirement_id:
                    requirement = await requirement_controller.get(id=requirement_id)
                    requirement_dict = await requirement.to_dict()
                    item["requirement_name"] = requirement_dict.get("name", "")
                    item["requirement_tapd_url"] = requirement_dict.get("tapd_url", "")
                else:
                    item["requirement_name"] = ""
                    item["requirement_tapd_url"] = ""
            except Exception as e:
                logger.error(f"Error fetching requirement with id {requirement_id}: {str(e)}")
                item["requirement_name"] = ""
                item["requirement_tapd_url"] = ""

            # 处理测试步骤
            steps = item.get("steps", [])
            if steps:
                # 将步骤合并为一个字符串
                steps_str = ""
                for i, step in enumerate(steps):
                    steps_str += f"{i+1}. {step.get('description', '')}\n"
                item["steps_text"] = steps_str

                # 将预期结果合并为一个字符串
                expected_results_str = ""
                for i, step in enumerate(steps):
                    expected_results_str += f"{i+1}. {step.get('expected_result', '')}\n"
                item["expected_results_text"] = expected_results_str
            else:
                item["steps_text"] = ""
                item["expected_results_text"] = ""

            data.append(item)

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 选择要导出的列并重命名
        columns_mapping = {
            "id": "用例ID",
            "title": "用例标题",
            "desc": "用例描述",
            "preconditions": "前置条件",
            "steps_text": "测试步骤",
            "expected_results_text": "预期结果",
            "postconditions": "后置条件",
            "priority": "优先级",
            "status": "状态",
            "tags": "标签",
            "project_name": "项目名称",
            "requirement_name": "需求名称",
            "requirement_tapd_url": "TAPD链接",
            "creator": "创建者",
            "created_at": "创建时间"
        }

        # 选择并重命名列
        export_columns = [col for col in columns_mapping.keys() if col in df.columns]
        df = df[export_columns]
        df = df.rename(columns=columns_mapping)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='测试用例', index=False)

            # 获取xlsxwriter工作簿和工作表对象
            workbook = writer.book
            worksheet = writer.sheets['测试用例']

            # 设置列宽
            for i, col in enumerate(df.columns):
                # 计算列宽度（根据内容长度）
                max_len = max(
                    df[col].astype(str).map(len).max(),  # 最长数据
                    len(col)  # 标题长度
                ) + 2  # 额外空间

                # 限制最大宽度
                col_width = min(max_len, 50)
                worksheet.set_column(i, i, col_width)

            # 添加自动筛选
            worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)

            # 设置标题行格式
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })

            # 写入标题行
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)

        # 设置文件指针到开始
        output.seek(0)

        # 生成文件名
        filename = "测试用例导出.xlsx"

        # 返回Excel文件
        return StreamingResponse(
            output,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        logger.error(f"导出测试用例失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"导出测试用例失败: {str(e)}")